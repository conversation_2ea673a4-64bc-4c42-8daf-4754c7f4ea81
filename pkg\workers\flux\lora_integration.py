"""
Flux LoRA 集成模块
负责 LoRA 模型选择、应用和工作流集成
这是 Flux 工作流的专有特性
"""

import json
import os
from typing import Dict, Any, Optional, List, Tuple
import logging

from .flux_workflow_models import FluxParameters, LoRAConfig, LoRACategory
from ...workers.shared.shared_lora_manager import SharedLoraManager


class LoRAIntegration:
    """Flux LoRA 集成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.lora_manager = SharedLoraManager()
        
        # LoRA节点模板
        self.lora_node_template = {
            "class_type": "LoraLoader",
            "inputs": {
                "model": None,
                "clip": None,
                "lora_name": "",
                "strength_model": 0.8,
                "strength_clip": 0.8
            }
        }
    
    async def select_loras_for_prompt(self, params: FluxParameters, max_loras: int = 3,
                                    use_civitai: bool = False, civitai_query: str = "") -> List[LoRAConfig]:
        """
        根据提示词和参数选择合适的LoRA模型

        Args:
            params: Flux参数
            max_loras: 最大LoRA数量
            use_civitai: 是否使用Civitai搜索
            civitai_query: Civitai搜索关键词

        Returns:
            List[LoRAConfig]: 选择的LoRA配置列表
        """
        try:
            selected_loras = []

            # 第一步：本地模型匹配
            search_query = civitai_query if civitai_query else params.prompt
            selected_models = self.lora_manager.get_models_by_trigger(search_query)

            # 如果启用Civitai且本地模型不足，尝试搜索下载
            if use_civitai and len(selected_models) < max_loras:
                self.logger.info(f"本地模型不足，从Civitai搜索: {search_query}")
                await self._search_and_download_civitai_models(search_query, max_loras - len(selected_models))
                # 重新获取模型（可能包含新下载的）
                selected_models = self.lora_manager.get_models_by_trigger(search_query)
            
            # 转换为LoRAConfig格式
            for model in selected_models[:max_loras]:
                lora_config = LoRAConfig(
                    name=model.name,
                    filename=model.filename,
                    weight=model.weight,
                    category=self._map_category(model.category.value),
                    trigger_words=model.trigger_words,
                    description=model.description,
                    file_path=model.file_path,
                    is_local=model.is_local,
                    is_active=model.is_active
                )
                
                # 设置Civitai信息
                if model.civitai_id:
                    lora_config.civitai_id = model.civitai_id
                if model.civitai_url:
                    lora_config.civitai_url = model.civitai_url
                if model.rating is not None:
                    lora_config.rating = float(model.rating)
                if model.downloads is not None:
                    lora_config.downloads = int(model.downloads)
                
                selected_loras.append(lora_config)
            
            # 如果没有找到合适的LoRA，添加默认的细节增强LoRA
            if not selected_loras:
                default_lora = self._get_default_detail_lora()
                if default_lora:
                    selected_loras.append(default_lora)
            
            self.logger.info(f"为提示词选择了 {len(selected_loras)} 个LoRA模型")
            for lora in selected_loras:
                self.logger.info(f"  - {lora.name} (权重: {lora.weight}, 分类: {lora.category.value})")
            
            return selected_loras
            
        except Exception as e:
            self.logger.error(f"选择LoRA模型失败: {e}")
            # 返回默认LoRA
            default_lora = self._get_default_detail_lora()
            return [default_lora] if default_lora else []
    
    def apply_loras_to_workflow(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig]) -> Dict[str, Any]:
        """
        将LoRA模型应用到工作流
        
        Args:
            workflow_data: 原始工作流数据
            loras: LoRA配置列表
            
        Returns:
            Dict[str, Any]: 更新后的工作流数据
        """
        if not loras:
            self.logger.info("没有LoRA模型需要应用")
            return workflow_data
        
        try:
            updated_workflow = workflow_data.copy()
            
            # 查找现有的LoraLoader节点
            existing_lora_nodes = self._find_lora_nodes(updated_workflow)
            
            # 找到模型连接的起始点（通常是CheckpointLoader）
            checkpoint_node_id = self._find_checkpoint_node(updated_workflow)
            if not checkpoint_node_id:
                self.logger.error("找不到CheckpointLoader节点，无法应用LoRA")
                return workflow_data
            
            # 应用LoRA链
            last_model_output = (checkpoint_node_id, 0)
            last_clip_output = (checkpoint_node_id, 1)
            
            for i, lora in enumerate(loras):
                # 创建LoRA节点ID
                lora_node_id = f"lora_{i+1}"
                
                # 创建LoRA节点
                lora_node = self._create_lora_node(
                    lora,
                    last_model_output,
                    last_clip_output
                )
                
                # 添加到工作流
                updated_workflow[lora_node_id] = lora_node
                
                # 更新输出连接
                last_model_output = (lora_node_id, 0)
                last_clip_output = (lora_node_id, 1)
                
                self.logger.info(f"应用LoRA {lora.name} 到节点 {lora_node_id}")
            
            # 更新依赖这些输出的节点
            self._update_downstream_connections(
                updated_workflow,
                checkpoint_node_id,
                last_model_output,
                last_clip_output
            )
            
            self.logger.info(f"成功应用 {len(loras)} 个LoRA模型到工作流")
            return updated_workflow
            
        except Exception as e:
            self.logger.error(f"应用LoRA到工作流失败: {e}")
            return workflow_data
    
    def optimize_lora_weights(self, loras: List[LoRAConfig], quality_level: str = "standard") -> List[LoRAConfig]:
        """
        根据质量级别优化LoRA权重
        
        Args:
            loras: LoRA配置列表
            quality_level: 质量级别 ("fast", "standard", "high", "ultra")
            
        Returns:
            List[LoRAConfig]: 优化后的LoRA配置
        """
        optimized_loras = []
        
        # 权重调整系数
        weight_multipliers = {
            "fast": 0.6,      # 快速生成，降低权重
            "standard": 1.0,  # 标准权重
            "high": 1.2,      # 高质量，稍微增加权重
            "ultra": 1.1      # 超高质量，适度增加权重
        }
        
        multiplier = weight_multipliers.get(quality_level, 1.0)
        
        for lora in loras:
            optimized_lora = LoRAConfig(
                name=lora.name,
                filename=lora.filename,
                weight=min(1.0, lora.weight * multiplier),  # 确保不超过1.0
                category=lora.category,
                trigger_words=lora.trigger_words,
                description=lora.description,
                file_path=lora.file_path,
                civitai_id=lora.civitai_id,
                civitai_url=lora.civitai_url,
                rating=lora.rating,
                downloads=lora.downloads,
                is_local=lora.is_local,
                is_active=lora.is_active
            )
            optimized_loras.append(optimized_lora)
        
        self.logger.info(f"为质量级别 '{quality_level}' 优化LoRA权重 (系数: {multiplier})")
        return optimized_loras
    
    def get_lora_statistics(self) -> Dict[str, Any]:
        """
        获取LoRA模型统计信息
        """
        stats = {
            "total_loras": 0,
            "active_loras": 0,
            "local_loras": 0,
            "categories": {},
            "supported_triggers": []
        }
        try:
            flux_stats = {
                "total_loras": 0,
                "active_loras": 0,
                "local_loras": 0,
                "categories": {},
                "supported_triggers": []
            }
            # 尝试获取模型信息
            try:
                models = getattr(self.lora_manager, 'models', {})
                if models:
                    flux_stats["total_loras"] = len(models)
                    flux_stats["active_loras"] = len([m for m in models.values() if getattr(m, 'is_active', False)])
                    flux_stats["local_loras"] = len([m for m in models.values() if getattr(m, 'is_local', False)])
                    # 按分类统计
                    for model in models.values():
                        if hasattr(model, 'category'):
                            category = getattr(model.category, 'value', str(model.category))
                            flux_stats["categories"][category] = flux_stats["categories"].get(category, 0) + 1
                    # 收集触发词
                    all_triggers = set()
                    for model in models.values():
                        if hasattr(model, 'trigger_words'):
                            all_triggers.update(model.trigger_words)
                    flux_stats["supported_triggers"] = list(all_triggers)[:20]  # 限制数量
            except Exception as e:
                self.logger.warning(f"获取LoRA模型信息时出错: {e}")
                # 使用默认值
            # 合并统计信息
            stats.update(flux_stats)
            return stats
        except Exception as e:
            self.logger.error(f"获取LoRA统计失败: {e}")
            return {}
    
    def validate_lora_files(self, loras: List[LoRAConfig]) -> Tuple[List[LoRAConfig], List[str]]:
        """
        验证LoRA文件是否存在
        
        Args:
            loras: LoRA配置列表
            
        Returns:
            Tuple[List[LoRAConfig], List[str]]: (有效的LoRA, 错误信息)
        """
        valid_loras = []
        errors = []
        
        for lora in loras:
            try:
                # 检查本地文件
                if lora.is_local and lora.file_path:
                    if os.path.exists(lora.file_path):
                        valid_loras.append(lora)
                    else:
                        errors.append(f"LoRA文件不存在: {lora.file_path}")
                else:
                    # 远程模型或者没有指定路径，假设有效
                    valid_loras.append(lora)
                    
            except Exception as e:
                errors.append(f"验证LoRA {lora.name} 失败: {e}")
        
        return valid_loras, errors

    async def _search_and_download_civitai_models(self, query: str, max_models: int = 2):
        """
        从Civitai搜索并下载模型

        Args:
            query: 搜索关键词
            max_models: 最大下载模型数量
        """
        try:
            self.logger.info(f"从Civitai搜索模型: {query} (最多 {max_models} 个)")

            # 更新模型信息（搜索）
            result = await self.lora_manager.update_from_civitai(query=query, limit=max_models * 2)
            self.logger.info(f"Civitai搜索结果: {result}")

            # 获取新搜索到的远程模型
            all_models = self.lora_manager.get_all_models()
            remote_models = [m for m in all_models if not m.is_local and m.civitai_id]

            # 按评分排序，选择最好的模型下载
            remote_models.sort(key=lambda x: (x.rating or 0, x.downloads or 0), reverse=True)

            downloaded_count = 0
            for model in remote_models[:max_models]:
                if downloaded_count >= max_models:
                    break

                try:
                    self.logger.info(f"尝试下载模型: {model.name}")
                    local_path = await self.lora_manager.download_civitai_model(model.name)

                    if local_path:
                        self.logger.info(f"成功下载模型: {model.name} -> {local_path}")
                        downloaded_count += 1
                    else:
                        self.logger.warning(f"下载模型失败: {model.name}")

                except Exception as e:
                    self.logger.error(f"下载模型 {model.name} 时出错: {e}")

            if downloaded_count > 0:
                self.logger.info(f"成功下载 {downloaded_count} 个Civitai模型")
            else:
                self.logger.warning("未能下载任何Civitai模型")

        except Exception as e:
            self.logger.error(f"Civitai搜索下载失败: {e}")
    
    def _map_category(self, category_str: str) -> LoRACategory:
        """映射分类字符串到枚举"""
        category_mapping = {
            "detail": LoRACategory.DETAIL,
            "style": LoRACategory.STYLE,
            "character": LoRACategory.CHARACTER,
            "background": LoRACategory.BACKGROUND,
            "lighting": LoRACategory.LIGHTING,
            "texture": LoRACategory.TEXTURE
        }
        return category_mapping.get(category_str, LoRACategory.DETAIL)
    
    def _get_default_detail_lora(self) -> Optional[LoRAConfig]:
        """获取默认的细节增强LoRA"""
        try:
            # 查找细节增强类的LoRA
            try:
                models = getattr(self.lora_manager, 'models', {})
                for model in models.values():
                    if (hasattr(model, 'category') and hasattr(model.category, 'value') and 
                        model.category.value == "detail" and getattr(model, 'is_active', False)):
                        return LoRAConfig(
                            name=model.name,
                            filename=model.filename,
                            weight=0.8,
                            category=LoRACategory.DETAIL,
                            trigger_words=model.trigger_words,
                            description=model.description,
                            file_path=model.file_path,
                            is_local=model.is_local,
                            is_active=model.is_active
                        )
            except AttributeError:
                pass
            
            # 如果没有找到，返回一个通用的默认配置
            return LoRAConfig(
                name="flux_detail_enhancer",
                filename="flux_detail_enhancer.safetensors",
                weight=0.8,
                category=LoRACategory.DETAIL,
                trigger_words=["detail", "high quality", "masterpiece"],
                description="默认细节增强LoRA",
                is_local=False,
                is_active=True
            )
            
        except Exception as e:
            self.logger.error(f"获取默认LoRA失败: {e}")
            return None
    
    def _find_lora_nodes(self, workflow_data: Dict[str, Any]) -> List[str]:
        """查找现有的LoRA节点"""
        lora_nodes = []
        
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                if node_data.get("class_type") == "LoraLoader":
                    lora_nodes.append(node_id)
        
        return lora_nodes
    
    def _find_checkpoint_node(self, workflow_data: Dict[str, Any]) -> Optional[str]:
        """查找CheckpointLoader节点"""
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict):
                if node_data.get("class_type") == "CheckpointLoaderSimple":
                    return node_id
        return None
    
    def _create_lora_node(self, lora: LoRAConfig, model_input: Tuple[str, int], clip_input: Tuple[str, int]) -> Dict[str, Any]:
        """创建LoRA节点"""
        return {
            "class_type": "LoraLoader",
            "inputs": {
                "model": list(model_input),
                "clip": list(clip_input),
                "lora_name": lora.filename,
                "strength_model": lora.weight,
                "strength_clip": lora.weight
            }
        }
    
    def _update_downstream_connections(self, workflow_data: Dict[str, Any], 
                                     checkpoint_node_id: str,
                                     final_model_output: Tuple[str, int],
                                     final_clip_output: Tuple[str, int]):
        """更新下游节点的连接"""
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and "inputs" in node_data:
                inputs = node_data["inputs"]
                
                # 更新模型连接
                if "model" in inputs:
                    current_connection = inputs["model"]
                    if (isinstance(current_connection, list) and 
                        len(current_connection) >= 2 and
                        current_connection[0] == checkpoint_node_id and
                        current_connection[1] == 0):
                        inputs["model"] = list(final_model_output)
                
                # 更新CLIP连接
                if "clip" in inputs:
                    current_connection = inputs["clip"]
                    if (isinstance(current_connection, list) and 
                        len(current_connection) >= 2 and
                        current_connection[0] == checkpoint_node_id and
                        current_connection[1] == 1):
                        inputs["clip"] = list(final_clip_output)
                
                # 更新positive/negative连接 (通常连接到CLIP输出)
                for conn_type in ["positive", "negative"]:
                    if conn_type in inputs:
                        current_connection = inputs[conn_type]
                        if (isinstance(current_connection, list) and 
                            len(current_connection) >= 2):
                            # 如果原来连接到文本编码器，需要更新其CLIP输入
                            pass  # 文本编码器会自动使用更新后的CLIP
    
    def create_lora_summary(self, loras: List[LoRAConfig]) -> Dict[str, Any]:
        """创建LoRA使用摘要"""
        return {
            "total_loras": len(loras),
            "lora_details": [
                {
                    "name": lora.name,
                    "weight": lora.weight,
                    "category": lora.category.value,
                    "triggers": lora.trigger_words[:3],  # 只显示前3个触发词
                    "is_local": lora.is_local
                }
                for lora in loras
            ],
            "combined_weight": sum(lora.weight for lora in loras),
            "categories_used": list(set(lora.category.value for lora in loras))
        }


# 全局单例
lora_integration: Optional[LoRAIntegration] = None

def get_lora_integration() -> LoRAIntegration:
    """获取LoRA集成器单例"""
    global lora_integration
    if lora_integration is None:
        lora_integration = LoRAIntegration()
    return lora_integration 