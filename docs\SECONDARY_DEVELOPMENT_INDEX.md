# 二次开发代码索引

## 📋 概述

本文档自动生成于 2025-07-08 13:23:08，记录了所有带有 `[二次开发]` 标识的代码文件。

## 🏷️ 标识规范

### 文件头标识格式
```python
"""
[二次开发] 模块名称
功能描述

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：[具体功能描述]
- 维护者：开发团队
- 最后更新：[日期]
- 相关任务：[任务编号]
- 依赖关系：[依赖说明]
"""
```

### 目录标识格式
```python
"""
[二次开发目录] 目录名称
此目录下的所有文件均为二次开发代码，不属于langbot原生代码
"""
```

## 📁 二次开发代码索引


### pkg/workers/flux (4个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `flux_prompt_optimizer.py` | 负责LLM润色、提示词增强等（专用于aigen/ControlNet/LoRA等Flux管线） | 🔥 活跃 | 2025-07-08 |
| `flux_workflow_manager.py` | 统一协调所有 Flux 专有模块，提供完整的 Flux 工作流执行能力 | 🔥 活跃 | 2025-07-08 |
| `image_file_manager.py` | 负责保存用户图片到本地并管理文件路径 | 🔥 活跃 | 2025-07-08 |
| `seed_manager.py` | 负责种子历史记录、复用和管理 | 🔥 活跃 | 2025-07-08 |

### pkg/workers/kontext (5个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `custom_nodes.py` | 负责 FluxKontextProImageNode 等 Kontext 专有节点的处理 | 🔥 活跃 | 2025-07-08 |
| `kontext_prompt_optimizer.py` | 负责LLM润色、提示词增强等 | 🔥 活跃 | 2025-07-08 |
| `local_kontext_workflow_manager.py` | 功能描述待补充 | 🔥 活跃 | 2025-07-08 |
| `multi_image_handler.py` | 负责处理 1-3 张图片的输入、拼接和管理 | 🔥 活跃 | 2025-07-08 |
| `prompt_upsampler.py` | 功能描述待补充 | 🔥 活跃 | 2025-07-08 |

### pkg/workers/shared (2个文件)

| 文件名 | 功能描述 | 状态 | 最后更新 |
|--------|----------|------|----------|
| `shared_comfyui_client.py` | 功能描述待补充 | 🔥 活跃 | 2025-07-08 |
| `shared_lora_manager.py` | 功能描述待补充 | 🔥 活跃 | 2025-07-08 |

## 📊 统计信息

### 代码分布统计
- **总文件数**: 11个二次开发文件
- **Flux模块**: 4个文件
- **Kontext模块**: 5个文件
- **Kontext API模块**: 0个文件
- **共享模块**: 2个文件
- **核心模块**: 0个文件
- **适配器模块**: 0个文件

### 状态分布统计
- **🔥 活跃**: 11个文件

## 🔧 维护说明

### 自动生成
- 此文档由 `check_dev_index.py` 脚本自动生成
- 基于文件头注释中的 `[二次开发]` 标识进行扫描
- 建议定期运行脚本更新索引

### 手动维护
- 添加新文件时，确保包含标准化的 `[二次开发]` 标识
- 修改文件功能时，更新文件头注释中的描述
- 删除文件时，运行脚本重新生成索引

## 📋 使用说明

### 运行检查脚本
```bash
python check_dev_index.py
```

### 生成索引文档
```bash
python check_dev_index.py --generate
```

### 检查完整性
```bash
python check_dev_index.py --check
```

---

**维护者**: 开发团队  
**生成时间**: 2025-07-08 13:23:08  
**状态**: 自动生成
