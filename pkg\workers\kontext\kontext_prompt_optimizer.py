"""
[二次开发] Kontext 提示词优化模块
负责LLM润色、提示词增强等

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext工作流的提示词优化和LLM翻译
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：REQ-01 需求范围定义
- 依赖关系：依赖langbot的LLM模型管理器和查询对象
"""
import re
import logging
from typing import Dict, Optional, Any

class KontextPromptOptimizer:
    """
    LLM提示词优化与增强
    """
    def __init__(self, lora_dict: Optional[Dict[str, str]] = None):
        self.lora_dict = lora_dict or {}
        self.logger = logging.getLogger(__name__)

    def optimize_prompt(self, prompt: str) -> str:
        """
        对输入的prompt进行优化（如润色、去噪、增强等）
        """
        if not prompt or not prompt.strip():
            return prompt

        # 基础清理
        cleaned_prompt = self._clean_prompt(prompt)

        # 简单的英文优化（如果是中文，建议使用LLM翻译）
        if self._is_chinese(cleaned_prompt):
            # 如果是中文，返回原文（需要LLM翻译）
            self.logger.info("检测到中文提示词，建议使用LLM翻译")
            return cleaned_prompt

        # 英文提示词优化
        optimized = self._enhance_english_prompt(cleaned_prompt)
        return optimized

    def _clean_prompt(self, prompt: str) -> str:
        """清理提示词：保留中文标点，仅清理英文特殊符号"""
        cleaned = prompt.strip()
        # 检查是否包含中文
        if self._is_chinese(cleaned):
            # 仅移除英文特殊符号，保留中文标点
            # 中文标点范围：\u3000-\u303F，\uFF00-\uFFEF
            cleaned = re.sub(r'[A-Za-z0-9@#\$%\^&\*_+=<>\|~`]', '', cleaned)
            # 不移除中文逗号、句号、顿号等
        else:
            # 英文场景下移除特殊字符（保留基本标点）
            cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\']', '', cleaned)
        return cleaned

    def _is_chinese(self, text: str) -> bool:
        """检测是否包含中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))

    def _enhance_english_prompt(self, prompt: str) -> str:
        """增强英文提示词"""
        # 基础艺术风格增强
        enhanced = prompt

        # 如果没有质量词，添加基础质量描述
        quality_keywords = ['high quality', 'detailed', 'masterpiece', 'best quality']
        if not any(keyword in enhanced.lower() for keyword in quality_keywords):
            enhanced = f"high quality, detailed, {enhanced}"

        # 确保语法正确
        enhanced = enhanced.strip()
        if not enhanced.endswith('.'):
            enhanced += '.'

        return enhanced

    async def optimize_prompt_with_llm(self, prompt: str, query: Any) -> str:
        """
        使用LLM优化提示词（完整版本）
        """
        try:
            # 检测语言
            if self._is_chinese(prompt):
                # 中文需要翻译和优化
                return await self._translate_and_optimize(prompt, query)
            else:
                # 英文直接优化
                return await self._optimize_english_with_llm(prompt, query)
        except Exception as e:
            self.logger.error(f"LLM提示词优化失败: {e}")
            return self.optimize_prompt(prompt)  # 降级到基础优化

    async def _translate_and_optimize(self, chinese_prompt: str, query: Any) -> str:
        """
        翻译中文并优化（Kontext专用系统提示词，采用自然语言+专业修饰词最佳实践）
        """
        try:
            system_prompt = '''You are a professional AI image editing and style transfer prompt optimizer. Please convert the user's Chinese description into a high-quality English editing instruction using fluent, natural language sentences. Your instruction should:

- Clearly describe the target style (watercolor, oil painting, cyberpunk, architectural rendering, etc.)
- Specify the desired changes (color, lighting, material, atmosphere, structural adjustments, etc.)
- Indicate which features to retain or emphasize
- Convey the overall effect or emotion to achieve
- Optionally reference the use of a reference image (e.g., "use the color palette and brushstroke texture from the second image")
- Optionally include negative prompts (elements to avoid)

**Requirements:**
1. Use natural language, not just a list of keywords.
2. Add rich modifiers and highlight style, details, and mood.
3. Limit to 200 words.
4. Only return the optimized English instruction, nothing else.

**Examples:**
1. Input: 将这张建筑照片转换为夜景效果，增加灯光反射，突出玻璃材质
   Output: Transform this architectural photo into a vibrant night scene, enhance the reflections and lighting on the glass surfaces, emphasize the building's modern design, create a lively urban atmosphere.

2. Input: 参考第二张图的色彩和风格，把第一张建筑图变成水彩画效果
   Output: Convert the first architectural image into a watercolor painting style, using the color palette and brushstroke texture from the second reference image, maintain the building's structure and perspective, add a soft and artistic touch.

3. Input: 让人物看起来更年轻，肤色更明亮，背景变为春天的花园
   Output: Make the person appear younger with brighter skin tones, change the background to a spring garden filled with blooming flowers, enhance the overall freshness and vitality of the image.

4. Input: 把这张猫的照片变成油画风格，突出毛发的质感
   Output: Transform this cat photo into an oil painting style, emphasize the texture of the fur, use rich and warm colors, add visible brushstrokes for an artistic effect.

5. Input: 将这张风景照改为黄昏时分，增加金色阳光和长长的影子
   Output: Edit this landscape photo to depict sunset, add golden sunlight and long shadows, create a serene and nostalgic mood, enhance the depth and contrast.

6. Input: 将人物照片转换为赛博朋克风格，添加霓虹灯和未来城市背景
   Output: Convert the portrait into a cyberpunk style, add neon lights and a futuristic cityscape in the background, use vibrant and contrasting colors, create a dynamic and edgy atmosphere.

7. Input: 让这张图片看起来像素描，保留主要轮廓，去除多余细节
   Output: Make this image resemble a pencil sketch, retain the main outlines, remove unnecessary details, use soft shading for a minimalist and artistic look.'''
            user_prompt = f"请优化这个中文提示词：{chinese_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                return optimized.strip()
            else:
                return self._prompt_for_english(chinese_prompt)
        except Exception as e:
            self.logger.error(f"翻译优化失败: {e}")
            return self._prompt_for_english(chinese_prompt)

    async def _optimize_english_with_llm(self, english_prompt: str, query: Any) -> str:
        """
        优化英文提示词（Kontext专用系统提示词，采用自然语言+专业修饰词最佳实践）
        """
        try:
            system_prompt = '''You are a professional AI image editing and style transfer prompt optimizer. Please optimize the user's English instruction using fluent, natural language sentences. Your instruction should:

- Clearly describe the target style (watercolor, oil painting, cyberpunk, architectural rendering, etc.)
- Specify the desired changes (color, lighting, material, atmosphere, structural adjustments, etc.)
- Indicate which features to retain or emphasize
- Convey the overall effect or emotion to achieve
- Optionally reference the use of a reference image (e.g., "use the color palette and brushstroke texture from the second image")
- Optionally include negative prompts (elements to avoid)

**Requirements:**
1. Use natural language, not just a list of keywords.
2. Add rich modifiers and highlight style, details, and mood.
3. Limit to 200 words.
4. Only return the optimized English instruction, nothing else.

**Examples:**
1. Transform this architectural photo into a vibrant night scene, enhance the reflections and lighting on the glass surfaces, emphasize the building's modern design, create a lively urban atmosphere.
2. Convert the first architectural image into a watercolor painting style, using the color palette and brushstroke texture from the second reference image, maintain the building's structure and perspective, add a soft and artistic touch.
3. Make the person appear younger with brighter skin tones, change the background to a spring garden filled with blooming flowers, enhance the overall freshness and vitality of the image.
4. Transform this cat photo into an oil painting style, emphasize the texture of the fur, use rich and warm colors, add visible brushstrokes for an artistic effect.
5. Edit this landscape photo to depict sunset, add golden sunlight and long shadows, create a serene and nostalgic mood, enhance the depth and contrast.
6. Convert the portrait into a cyberpunk style, add neon lights and a futuristic cityscape in the background, use vibrant and contrasting colors, create a dynamic and edgy atmosphere.
7. Make this image resemble a pencil sketch, retain the main outlines, remove unnecessary details, use soft shading for a minimalist and artistic look.'''
            user_prompt = f"请优化这个英文提示词：{english_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                return optimized.strip()
            else:
                return self._enhance_english_prompt(english_prompt)
        except Exception as e:
            self.logger.error(f"英文优化失败: {e}")
            return self._enhance_english_prompt(english_prompt)

    async def _call_llm_for_optimization(self, system_prompt: str, user_prompt: str, query: Any) -> Optional[str]:
        """调用LLM进行优化"""
        try:
            # 检查query对象
            if not query or not hasattr(query, 'pipeline_config') or not query.pipeline_config:
                self.logger.warning("无效的query对象，无法调用LLM")
                return None

            # 获取LLM模型配置
            model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
            if not model_uuid:
                self.logger.warning("未配置LLM模型，无法进行提示词优化")
                return None

            # 获取应用实例
            if not hasattr(query, 'ap') or not query.ap:
                self.logger.warning("无法获取应用实例，无法调用LLM")
                return None

            # 查找对应的RuntimeLLMModel
            runtime_llm_model = None
            for model in query.ap.model_mgr.llm_models:
                if model.model_entity.uuid == model_uuid:
                    runtime_llm_model = model
                    break

            if not runtime_llm_model:
                self.logger.warning(f"未找到模型 {model_uuid}，无法进行提示词优化")
                return None

            # 导入必要的模块
            from ...provider import entities as llm_entities

            # 创建消息
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]

            # 调用LLM
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],  # 不需要工具调用
                extra_args={},
            )

            # 提取响应文本
            response_text = self._extract_response_text(result)
            if response_text:
                self.logger.info("LLM提示词优化成功")
                return response_text.strip()
            else:
                self.logger.warning("LLM返回空响应")
                return None

        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            return None

    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本"""
        response_text = ""

        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)

        return response_text.strip()

    def _prompt_for_english(self, chinese_prompt: str) -> str:
        """提示用户使用英文提示词"""
        return f"⚠️ LLM翻译服务当前不可用，请直接提供英文提示词。原始输入：{chinese_prompt}"

    def _basic_translate_and_optimize(self, chinese_prompt: str) -> str:
        """基础翻译和优化（当LLM不可用时的回退方案）- Kontext专用"""
        try:
            # Kontext专用的关键词映射翻译（图像编辑相关）
            translation_map = {
                # 编辑动作相关
                '编辑': 'edit', '修改': 'modify', '调整': 'adjust', '优化': 'optimize',
                '变换': 'transform', '转换': 'convert', '改变': 'change',
                '增强': 'enhance', '美化': 'beautify', '润色': 'polish',

                # 风格相关
                '风格': 'style', '艺术': 'artistic', '现代': 'modern', '古典': 'classical',
                '抽象': 'abstract', '写实': 'realistic', '卡通': 'cartoon',
                '油画': 'oil painting', '水彩': 'watercolor', '素描': 'sketch',
                '赛博朋克': 'cyberpunk', '蒸汽朋克': 'steampunk',

                # 颜色和光照
                '颜色': 'color', '色彩': 'color', '明亮': 'bright', '暗': 'dark',
                '鲜艳': 'vibrant', '柔和': 'soft', '对比': 'contrast',
                '饱和': 'saturated', '淡': 'light', '深': 'deep',

                # 图像质量
                '清晰': 'sharp', '模糊': 'blur', '细节': 'detail', '质量': 'quality',
                '高清': 'high resolution', '精细': 'fine', '粗糙': 'rough',

                # 构图和视角
                '构图': 'composition', '视角': 'perspective', '角度': 'angle',
                '特写': 'close-up', '全景': 'panoramic', '俯视': 'top view',

                # 背景和环境
                '背景': 'background', '前景': 'foreground', '环境': 'environment',
                '场景': 'scene', '氛围': 'atmosphere', '情绪': 'mood',

                # 人物和对象
                '人物': 'character', '脸部': 'face', '表情': 'expression',
                '姿势': 'pose', '动作': 'action', '服装': 'clothing',

                # 特效和处理
                '特效': 'effects', '滤镜': 'filter', '光效': 'lighting effects',
                '阴影': 'shadow', '高光': 'highlight', '反射': 'reflection'
            }

            # 进行基础翻译
            translated = chinese_prompt
            for chinese, english in translation_map.items():
                translated = translated.replace(chinese, english)

            # 添加Kontext专用的修饰词
            if any(word in chinese_prompt for word in ['编辑', '修改', '调整']):
                translated += ", precise editing, maintain original structure"

            if any(word in chinese_prompt for word in ['风格', '艺术']):
                translated += ", artistic style transfer, creative enhancement"

            if any(word in chinese_prompt for word in ['颜色', '色彩']):
                translated += ", color correction, vibrant colors"

            # 添加基础的图像编辑质量描述
            translated += ", high quality image editing, professional result"

            self.logger.info(f"Kontext基础翻译优化: {chinese_prompt} -> {translated}")
            return translated.strip()

        except Exception as e:
            self.logger.error(f"基础翻译优化失败: {e}")
            # 最后的回退：返回原始提示词加上基础修饰
            return f"{chinese_prompt}, high quality editing, professional result"

    def apply_lora(self, prompt: str, lora_name: str) -> str:
        """
        根据lora_name将LoRA模型指令插入到prompt中
        """
        lora_tag = self.lora_dict.get(lora_name, f'<lora:{lora_name}>')
        return f"{lora_tag} {prompt}"

    def analyze_prompt(self, prompt: str) -> Dict[str, str]:
        """
        分析prompt，提取关键信息（如风格、主题等）
        """
        analysis = {
            "length": str(len(prompt)),
            "language": "chinese" if self._is_chinese(prompt) else "english",
            "has_quality_keywords": str(any(kw in prompt.lower() for kw in ['high quality', 'detailed', 'masterpiece'])),
            "word_count": str(len(prompt.split()))
        }
        return analysis

kontext_prompt_optimizer = KontextPromptOptimizer()