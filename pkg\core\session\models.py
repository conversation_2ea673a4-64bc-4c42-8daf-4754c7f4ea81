"""
会话管理数据模型
统一的会话状态、工作流类型和会话对象定义
"""

import time
import hashlib
import uuid
from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime


class WorkflowType(Enum):
    """工作流类型枚举"""
    AIGEN = "aigen"           # 本地Flux工作流
    KONTEXT = "kontext"       # 本地kontext工作流
    KONTEXT_API = "kontext_api"  # 远程API工作流
    UNKNOWN = "unknown"       # 未知类型


class FluxImageType(Enum):
    """Flux工作流图片类型枚举"""
    CONTROL = "control"       # ControlNet控制图
    REFERENCE = "reference"   # Redux参考图
    MIXED = "mixed"           # 混合用途
    UNKNOWN = "unknown"       # 未知类型


class SessionState(Enum):
    """会话状态枚举"""
    IDLE = "idle"                      # 空闲状态
    COLLECTING = "collecting"          # 收集参数中
    WAITING_FOR_PROMPT = "waiting_for_prompt"     # 等待提示词
    WAITING_FOR_IMAGES = "waiting_for_images"     # 等待图片
    WAITING_FOR_IMAGE_TYPES = "waiting_for_image_types"  # 等待图片类型确认（Flux专用）
    WAITING_FOR_WORKFLOW_SELECTION = "waiting_for_workflow_selection"  # 等待用户选择工作流
    READY = "ready"                    # 准备执行
    READY_FOR_GENERATION = "ready_for_generation"  # 准备生成
    PROCESSING = "processing"          # 执行中
    GENERATING = "generating"          # 正在生成
    COMPLETED = "completed"            # 已完成
    CANCELLED = "cancelled"            # 已取消
    TIMEOUT = "timeout"                # 超时


def session_state_from_string(state_str: str) -> SessionState:
    """从字符串转换为SessionState"""
    try:
        return SessionState(state_str)
    except ValueError:
        return SessionState.IDLE


@dataclass
class SessionImage:
    """会话中的图片信息"""
    data: bytes
    purpose: str = "reference"  # reference, sketch, control, input
    source: str = "upload"      # upload, quoted, generated
    flux_image_type: FluxImageType = FluxImageType.UNKNOWN  # Flux专用图片类型
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_hash(self) -> str:
        """获取图片哈希值"""
        return hashlib.md5(self.data).hexdigest()
    
    def get_size(self) -> int:
        """获取图片大小（字节）"""
        return len(self.data)
    
    def is_control_image(self) -> bool:
        """是否为控制图"""
        return self.flux_image_type == FluxImageType.CONTROL
    
    def is_reference_image(self) -> bool:
        """是否为参考图"""
        return self.flux_image_type == FluxImageType.REFERENCE
    
    def is_mixed_image(self) -> bool:
        """是否为混合用途图片"""
        return self.flux_image_type == FluxImageType.MIXED


@dataclass
class SessionMessage:
    """会话中的单条消息"""
    content: str
    timestamp: float = field(default_factory=time.time)
    message_type: str = "user_text"  # user_text, trigger, system
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        self.content = self.content.strip()
    
    def is_trigger_message(self) -> bool:
        """是否为触发消息"""
        return self.message_type == "trigger"
    
    def is_user_text(self) -> bool:
        """是否为用户文本"""
        return self.message_type == "user_text"
    
    def has_content(self) -> bool:
        """是否有内容"""
        return bool(self.content.strip())


@dataclass
class WorkflowSession:
    """统一工作流会话对象"""
    session_id: str
    user_id: str
    chat_id: str = ""
    workflow_type: WorkflowType = WorkflowType.UNKNOWN
    state: SessionState = SessionState.COLLECTING

    # 核心数据 - 多条消息支持
    messages: List[SessionMessage] = field(default_factory=list)
    prompt: str = ""  # 保留用于兼容性，实际使用 messages
    images: List[SessionImage] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)

    # 引用消息相关
    quoted_text: str = ""
    quoted_images: List[bytes] = field(default_factory=list)

    # 路由结果
    routing_result: Optional[Any] = None  # 存储路由决策结果

    # 工作流文件强制指定
    forced_workflow_file: Optional[str] = None  # 强制使用指定的工作流文件

    # 时间管理
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    timeout_minutes: int = 10

    # 工作流限制
    max_images: int = 3
    min_images: int = 0

    # 🔥 兼容性接口：为了兼容langbot原生pipeline处理器
    _using_conversation: Optional[Any] = field(default=None, init=False)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.session_id:
            self.session_id = str(uuid.uuid4())

        # 根据工作流类型设置默认限制
        if self.workflow_type == WorkflowType.AIGEN:
            self.min_images = 0
            self.max_images = 3
        elif self.workflow_type == WorkflowType.KONTEXT:
            self.min_images = 1
            self.max_images = 3
        elif self.workflow_type == WorkflowType.KONTEXT_API:
            self.min_images = 1
            self.max_images = 3

        # 🔥 初始化兼容性对象
        self._init_compatibility_interface()
    
    def is_active(self) -> bool:
        """检查会话是否活跃（未超时）"""
        return (time.time() - self.updated_at) < (self.timeout_minutes * 60)
    
    def is_expired(self) -> bool:
        """检查会话是否已过期"""
        return not self.is_active()
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = time.time()
    
    def update_activity(self):
        """更新最后活动时间"""
        self.update_timestamp()
    
    def add_image(self, image_data: bytes, purpose: str = "reference", source: str = "upload") -> bool:
        """
        添加图片到会话
        返回是否成功添加
        """
        # 检查是否已达到最大图片数
        if self.get_image_count() >= self.max_images:
            return False
        
        # 检查是否重复
        new_hash = hashlib.md5(image_data).hexdigest()
        for existing_image in self.images:
            if existing_image.get_hash() == new_hash:
                return False  # 图片已存在
        
        session_image = SessionImage(
            data=image_data,
            purpose=purpose,
            source=source
        )
        self.images.append(session_image)
        self.update_activity()
        return True
    
    def add_quoted_images(self, images: List[bytes]):
        """添加引用图片"""
        for image_data in images:
            self.add_image(image_data, purpose="reference", source="quoted")
    
    def set_quoted_text(self, text: str):
        """设置引用文本"""
        self.quoted_text = text
        self.update_activity()
    
    def set_prompt(self, prompt: str):
        """设置提示词（兼容性方法，实际会添加为消息）"""
        self.prompt = prompt.strip()
        # 🔥 新增：同时添加到消息历史中
        if prompt.strip():
            # 检查是否为触发消息（包含触发词）
            message_type = "trigger" if any(keyword in prompt.lower() for keyword in ["aigen", "kontext"]) else "user_text"
            self.add_message(prompt.strip(), message_type)
        self.update_activity()
    
    def add_message(self, content: str, message_type: str = "user_text", metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加新消息到会话历史
        
        Args:
            content: 消息内容
            message_type: 消息类型 (user_text, trigger, system)
            metadata: 可选的元数据
            
        Returns:
            是否成功添加
        """
        if not content.strip():
            return False
        
        message = SessionMessage(
            content=content.strip(),
            message_type=message_type,
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        
        # 🔥 同步更新prompt字段（用于兼容性）
        if message_type in ["trigger", "user_text"]:
            self.prompt = self.get_all_messages_text()
        
        self.update_activity()
        return True
    
    def get_all_messages_text(self) -> str:
        """获取所有用户消息的文本内容（连接成单一字符串）"""
        user_messages = [msg for msg in self.messages if msg.message_type in ["trigger", "user_text"]]
        return " ".join(msg.content for msg in user_messages if msg.has_content())
    
    def get_conversation_context(self) -> Dict[str, Any]:
        """
        获取完整的会话上下文，供LLM分析使用
        
        Returns:
            包含消息历史、图片信息等的完整上下文
        """
        return {
            "messages": [
                {
                    "content": msg.content,
                    "type": msg.message_type,
                    "timestamp": msg.timestamp
                }
                for msg in self.messages if msg.has_content()
            ],
            "images": [
                {
                    "purpose": img.purpose,
                    "source": img.source,
                    "flux_type": img.flux_image_type.value,
                    "timestamp": img.timestamp
                }
                for img in self.images
            ],
            "workflow_type": self.workflow_type.value,
            "session_duration": time.time() - self.created_at,
            "quoted_text": self.quoted_text if self.quoted_text else None
        }
    
    def get_trigger_message(self) -> Optional[SessionMessage]:
        """获取触发消息（包含工作流触发词的消息）"""
        for msg in self.messages:
            if msg.is_trigger_message():
                return msg
        return None
    
    def get_user_messages(self) -> List[SessionMessage]:
        """获取所有用户消息（不包括系统消息）"""
        return [msg for msg in self.messages if msg.message_type in ["trigger", "user_text"]]
    
    def get_latest_message(self) -> Optional[SessionMessage]:
        """获取最新的消息"""
        if self.messages:
            return self.messages[-1]
        return None
    
    def has_messages(self) -> bool:
        """检查是否有消息"""
        return len(self.messages) > 0 and any(msg.has_content() for msg in self.messages)
    
    def get_message_count(self) -> int:
        """获取消息数量"""
        return len([msg for msg in self.messages if msg.has_content()])
    
    def can_add_image(self) -> bool:
        """检查是否还能添加图片"""
        return self.get_image_count() < self.max_images
    
    def has_enough_images(self) -> bool:
        """检查是否有足够的图片"""
        return self.get_image_count() >= self.min_images
    
    def has_prompt(self) -> bool:
        """检查是否有提示词（兼容性方法，实际检查消息历史）"""
        # 🔥 新增：优先检查消息历史，fallback到旧的prompt字段
        return self.has_messages() or bool(self.prompt.strip())
    
    def is_ready_for_execution(self) -> bool:
        """检查是否准备好执行"""
        # 新的逻辑：只要有触发消息就可以执行，具体工作流选择在go指令时决定
        return (
            self.is_active() and
            self.has_prompt()  # 必须有提示词或消息
        )
    
    def can_execute(self) -> bool:
        """检查是否可以执行"""
        return self.is_ready_for_execution()
    
    def get_image_count(self) -> int:
        """获取图片数量"""
        return len(self.images)
    
    def get_image_purposes(self) -> List[str]:
        """获取图片用途列表"""
        return [img.purpose for img in self.images]
    
    def get_flux_image_types(self) -> List[FluxImageType]:
        """获取Flux图片类型列表"""
        return [img.flux_image_type for img in self.images]
    
    def get_control_images(self) -> List['SessionImage']:
        """获取控制图列表"""
        return [img for img in self.images if img.is_control_image()]
    
    def get_reference_images(self) -> List['SessionImage']:
        """获取参考图列表"""
        return [img for img in self.images if img.is_reference_image()]
    
    def get_mixed_images(self) -> List['SessionImage']:
        """获取混合用途图片列表"""
        return [img for img in self.images if img.is_mixed_image()]
    
    def has_unclassified_images(self) -> bool:
        """是否有未分类的图片"""
        return any(img.flux_image_type == FluxImageType.UNKNOWN for img in self.images)
    
    def set_image_flux_type(self, image_index: int, flux_type: FluxImageType) -> bool:
        """设置指定图片的Flux类型"""
        if 0 <= image_index < len(self.images):
            self.images[image_index].flux_image_type = flux_type
            self.update_activity()
            return True
        return False
    
    def needs_image_type_confirmation(self) -> bool:
        """是否需要图片类型确认（Flux工作流且有多张未分类图片）"""
        return (self.workflow_type == WorkflowType.AIGEN and 
                self.get_image_count() > 1 and 
                self.has_unclassified_images())
    
    def get_remaining_time(self) -> float:
        """获取剩余时间（秒）"""
        remaining = (self.timeout_minutes * 60) - (time.time() - self.updated_at)
        return max(0, remaining)
    
    def format_remaining_time(self) -> str:
        """格式化剩余时间"""
        remaining = self.get_remaining_time()
        if remaining <= 0:
            return "已超时"
        
        minutes = int(remaining // 60)
        seconds = int(remaining % 60)
        return f"{minutes}分{seconds}秒"
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取会话摘要信息"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'chat_id': self.chat_id,
            'workflow_type': self.workflow_type.value,
            'state': self.state.value,
            'prompt': self.prompt,
            'image_count': self.get_image_count(),
            'image_purposes': self.get_image_purposes(),
            'has_quoted_text': bool(self.quoted_text),
            'quoted_images_count': len(self.quoted_images),
            'remaining_time': self.format_remaining_time(),
            'is_ready': self.is_ready_for_execution(),
            'created_at': datetime.fromtimestamp(self.created_at).isoformat(),
            'updated_at': datetime.fromtimestamp(self.updated_at).isoformat()
        }

    # 🔥 兼容性接口方法 - 为了兼容langbot原生pipeline处理器
    def _init_compatibility_interface(self):
        """初始化兼容性接口"""
        # 创建一个模拟的conversation对象，兼容langbot原生Session
        self._using_conversation = WorkflowConversationAdapter(self)

    @property
    def using_conversation(self):
        """兼容langbot原生Session.using_conversation属性"""
        return self._using_conversation


class WorkflowConversationAdapter:
    """
    WorkflowSession的Conversation适配器
    模拟langbot原生Session.using_conversation的接口
    """

    def __init__(self, workflow_session: WorkflowSession):
        self.workflow_session = workflow_session
        self.uuid = None    # 兼容uuid属性
        # 创建一个兼容的messages列表
        self.messages = WorkflowMessagesList()


class WorkflowMessagesList:
    """
    模拟messages列表的行为，兼容langbot原生的messages操作
    """

    def __init__(self):
        self._messages = []

    def append(self, message):
        """兼容messages.append()调用"""
        # 对于工作流会话，我们不需要保存对话历史
        # 但为了兼容性，我们接受这个调用而不报错
        pass

    def extend(self, messages):
        """兼容messages.extend()调用"""
        # 对于工作流会话，我们不需要保存对话历史
        # 但为了兼容性，我们接受这个调用而不报错
        pass

    def __len__(self):
        """支持len()操作"""
        return 0

    def __iter__(self):
        """支持迭代操作"""
        return iter([])

    def __getitem__(self, index):
        """支持索引操作"""
        raise IndexError("WorkflowSession不保存对话历史")